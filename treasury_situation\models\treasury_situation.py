from odoo import api, fields, models
from datetime import timedelta
from datetime import date

import logging

_logger = logging.getLogger(__name__)

class TreasurySituation(models.Model):
    _name = 'treasury.situation'
    _description = 'Treasury Situation'
    _order = 'sequence'
    
    annee = fields.Char(string='Année')
    source = fields.Char(string='Source')
    name = fields.Char(string='Name')
    payment_method = fields.Char(string='Methode de paiement')
    encaissement = fields.Float(string='Encaissement', digits='Product Price', readonly=True)
    decaissement = fields.Float(string='Décaissement', digits='Product Price', readonly=True)
    bank = fields.Char(string='Banque', readonly=True)
    payment_type = fields.Selection([('inbound', 'Encaissement'), ('outbound', 'Décaissement')], string='Type de paiement')
    maturity_date = fields.Date(string='Date de maturité')
    state = fields.Selection([('draft', 'Brouillon'), ('open', 'Ouvert'), ('paid', 'Payé'), ('rejected', 'Rejeté')], string='État')
    holder = fields.Many2one('res.partner', string='Holder')
    solde = fields.Float(string='Solde', store=True)
    is_initial_balance = fields.Boolean(string='Is Initial Balance', default=False)
    account_id = fields.Many2one('account.account', string='Bank Account')
    bank_balance = fields.Float(string='Solde Bancaire', readonly=True)
    sequence = fields.Integer(string='Sequence', default=10)
    statut = fields.Char(string='Statut')
    motif = fields.Char(string='Motif')
    credit = fields.Char(string='Crédit')

    @api.model
    def get_bank_balances(self):
        company_id = self.env.company.id
        domain = [('user_type_id', '=', 3)]  # Bank accounts
        
        # Filter by selected bank accounts if provided in context
        selected_accounts = self.env.context.get('selected_bank_accounts')
        if selected_accounts:
            domain.append(('id', 'in', selected_accounts))
            
        bank_accounts = self.env['account.account'].search(domain)
        _logger.info(f"Bank accounts: {bank_accounts}")
        _logger.info(f"Bank accounts ids: {bank_accounts.ids}")


        # First, delete existing initial balance records
        self.search([('is_initial_balance', '=', True)]).unlink()

        moves = self.env['account.move.line'].search([
            ('account_id', 'in', bank_accounts.ids),
            ('move_id.state', '=', 'posted'),
            ('company_id', '=', company_id),
        ])

        # Calculate balance per account
        balances = {}
        for line in moves:
            account = line.account_id
            if account not in balances:
                balances[account] = {
                    'account': account,
                    'balance': 0.0,
                    'name': account.name,
                }
            balances[account]['balance'] += line.debit - line.credit

        # Create initial balance records
        return balances
        

    def Get_payment_Operations(self):
        selected_journals = self.env.context.get('selected_journals', [])
        journals = self.env['account.journal'].browse(selected_journals)
        payments = self.env['account.payment'].search([
            ('act_journ', 'in', selected_journals),
            ('state', '=', 'posted'),
            ('payment_state','in',['in_cash', 'coffre', 'versed'])
            ('company_id', '=', self.env.company.id)
        ])
        
        # Merge the results and remove duplicates, then sort by effective maturity date
        days_to_add = int(self.env.context.get('date_matiurity_plus') or 0)
        # Prepare a list of (entry, effective_maturity_date)
        entries_with_dates = []
        for entry in payments:
            if entry.payment_type == 'outbound':
                effective_date = entry.maturity_date
            else:
                effective_date = entry.maturity_date + timedelta(days=days_to_add) if entry.maturity_date else None
            entries_with_dates.append((entry, effective_date))
        # Sort by effective maturity date (None values last)
        entries_with_dates.sort(key=lambda x: (x[1] is None, x[1]))
        treasury_entries = [entry for entry, _ in entries_with_dates]

        bank_balances = self.get_bank_balances()
        solde = 0
        sequence = 1
        
        # Create initial balances first
        for account_data in bank_balances.values():
            solde += account_data['balance']
            self.create({
                'name': f"Solde Actuel - {account_data['name']}",
                'bank': account_data['name'],
                'account_id': account_data['account'].id,
                'is_initial_balance': True,
                'maturity_date': False,
                'encaissement': account_data['balance'],
                'decaissement': 0.0,
                'solde': solde,
                'sequence': sequence,
            })
            sequence += 1

        # Process treasury entries in date order
        for entry in treasury_entries:
            if entry.maurity_date >= date(2025, 1, 1):
                days_to_add = int(self.env.context.get('date_matiurity_plus') or 0)
                maturity_date = ( entry.maturity_date if entry.payment_type == 'outbound' else entry.maturity_date + timedelta(days=days_to_add))
                debit = entry.move_id.line_ids.filtered(lambda line: line.debit > 0 and line.account_id.user_type_id.id in (3, 5))
                credit = entry.move_id.line_ids.filtered(lambda line: line.credit > 0 and line.account_id.user_type_id.id in (3, 5))
                solde += debit.amount_residual if entry.payment_type == 'inbound' else + credit.amount_residual
                statut = 'encours' if entry.state == 'versed' else ('en caisse' if entry.state == 'in_cash' else entry.state)
                self.create({
                    'annee': entry.maturity_date.year if entry.maturity_date else '',
                    'source': entry.name,
                    'payment_method': entry.payment_method_line_id.name if entry.payment_method_line_id else '',
                    'name': entry.num_treaty if entry.num_treaty else (entry.num_cheque if entry.num_cheque else entry.num_virement),
                    'is_initial_balance': False,
                    'maturity_date': maturity_date,
                    'payment_type': entry.payment_type,
                    'encaissement': debit.amount_residual if entry.payment_type == 'inbound' and debit else 0.0,
                    'decaissement': credit.amount_residual if entry.payment_type == 'outbound' and credit else 0.0,
                    'holder': entry.partner_id.id if entry.partner_id else False,
                    'solde': solde,
                    'sequence': sequence,
                    'statut': statut,
                    'crfedit':entry.credit_id.name if entry.credit_id else '',
                })
                sequence += 1

        return True

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Remove automatic refresh on search_read since we now use wizard"""
        return super(TreasurySituation, self).search_read(domain, fields, offset, limit, order)



