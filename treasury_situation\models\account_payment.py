from odoo import models,fields,api
class AccountPayment(models.Model):
    _inherit = 'account.payment'

    act_journ = fields.Many2one('account.journal', compute='_compute_act_journ',store=True)

    @api.depends('journal_id','treasury_id','treasury_id.move_line_id')
    def _compute_act_journ(self):
        for rec in self:
            rec.act_journ = rec.journal_id
            if rec.treasury_id and rec.treasury_id.move_line_id:
                if rec.treasury_id.move_line_id:
                    rec.act_journ = rec.treasury_id.move_line_id.journal_id
