<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
     <record id="view_treasury_situation_search" model="ir.ui.view">
        <field name="name">treasury.situation.search</field>
        <field name="model">treasury.situation</field>
        <field name="arch" type="xml">
            <search>
                <!-- Filters -->
                <field name="holder" string="Titulaire"/>
                <field name="act_journ" string="Journal Actif"/>
                <field name="maturity_date" string="Date Echéance"/>
                <field name="payment_type" string="Type de paiement"/>
                <!-- Group By -->
                <group expand="0" string="Group By">
                    <filter string="Date Echéance" 
                            name="maturity_date" 
                            domain="[]" 
                            context="{'group_by': 'maturity_date'}"/>
                    <filter string="Holder" 
                            name="holder" 
                            domain="[]" 
                            context="{'group_by': 'holder'}"/>
                    <filter string="Bank Account" 
                            name="account_id" 
                            domain="[]" 
                            context="{'group_by': 'account_id'}"/>
                </group>
            </search>
        </field>
    </record>

      <record id="view_treasury_situation_tree" model="ir.ui.view">
        <field name="name">treasury.situation.tree</field>
        <field name="model">treasury.situation</field>
        <field name="arch" type="xml">
            <tree>
                <field name="annee" string="Année"/>
                <field name="source" string="Source"/>
                <field name="payment_method" string="Moyen de Paiement"/>
                <field name="maturity_date" string="Date Echéance"/>
                <field name="name" string="Référence"/>
                <field name="payment_type" string="Type de Paiement"/>
                <field name="holder" string="Titulaire" />
                <field name="bank" string="Banque"/>
                <field name="encaissement" sum="Total Encaissements"/>
                <field name="decaissement" sum="Total Décaissements"/>
                <field name="statut"/>
                 <field name="solde" digits="[16,3]" decoration-danger="solde &lt; 0.0" decoration-bf="solde &lt; 0.0" />
                 <field name="credit"/>
                <field name="is_initial_balance" invisible="1"/>
                <field name="account_id" invisible="1"/>
            </tree>
        </field>
      </record>    
       <record id="action_treasury_situation" model="ir.actions.act_window">
        <field name="name">Treasury Situation</field>
        <field name="res_model">treasury.situation</field>
        <field name="view_mode">tree</field>
        <field name="limit">3000</field>

    </record>

      <!-- Add action for the view -->
         <record id="action_treasury_situation_wizard" model="ir.actions.act_window">
                <field name="name">Generate Treasury Situation</field>
                <field name="type">ir.actions.act_window</field>
                <field name="res_model">treasury.situation.wizard</field>
                <field name="search_view_id" ref="view_treasury_situation_search"/>
            </record>

        <menuitem id="menu_treasury_situation"
                  name="Situation de Trésorerie"
                  action="action_treasury_situation_wizard"
                  parent="l10n_tn_treasury.menu_statistique"
                  groups="account.group_account_user"
                  sequence="20"/>

        

        <!-- Form View -->
        <record id="view_treasury_situation_form" model="ir.ui.view">
            <field name="name">treasury.situation.form</field>
            <field name="model">treasury.situation</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="holder"/>
                                <field name="bank"/>
                                <field name="maturity_date"/>
                            </group>
                            <group>
                                <field name="bank_balance" attrs="{'invisible': [('is_initial_balance', '=', False)]}"/>
                                <field name="encaissement"/>
                                <field name="decaissement"/>
                                <field name="statut"/>
                                <field name="solde"/>
                                <field name="state"/>
                                <field name="is_initial_balance" invisible="1"/>
                                <field name="account_id"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

    </data>
</odoo> 